# Server configuration
PORT=8000
NODE_ENV=development
CORS_ORIGINS=*

# Database configuration (Neon PostgreSQL)
DB_HOST=db.example.neon.tech
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your-password
DB_NAME=filterx
DB_SSL=true

# Redis configuration (Upstash)
REDIS_URI=rediss://default:<EMAIL>:6379

# AI services
AKASH_CHAT_API_KEY=sk-xxxxxxxx
AKASH_CHAT_BASE_URL=https://chatapi.akash.network/api/v1
AKASH_CHAT_MODEL=Meta-Llama-3-3-70B-Instruct

# AI Model Tiers (optional - defaults provided)
AKASH_CHAT_MODEL_PRO=Qwen3-235B-A22B-FP8
AKASH_CHAT_MODEL_NORMAL=Meta-Llama-3-3-70B-Instruct
AKASH_CHAT_MODEL_FAST=Meta-Llama-3-1-8B-Instruct-FP8

MOONDREAM_API_KEY=your-moondream-api-key
MOONDREAM_BASE_URL=https://api.moondream.ai/v1

# Rate limiting
RATE_LIMIT_API_KEY_REQUESTS=10
RATE_LIMIT_FILTER_REQUESTS=30
RATE_LIMIT_WINDOW_MS=60000

# Caching
CACHE_DEFAULT_TTL=3600
CACHE_API_KEY_TTL=600
CACHE_RESPONSE_TTL=3600