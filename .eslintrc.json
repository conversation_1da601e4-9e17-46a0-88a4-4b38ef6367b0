{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking", "prettier"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-floating-promises": "error", "no-console": ["warn", {"allow": ["warn", "error", "info", "debug"]}], "prefer-const": "error", "no-var": "error", "eqeqeq": "error"}, "env": {"node": true, "es6": true}, "ignorePatterns": ["dist", "node_modules", "*.js", "*.d.ts"]}